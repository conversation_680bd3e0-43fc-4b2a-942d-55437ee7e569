package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.entity.param.PaymentCallbackParam;
import ai.showlab.bff.entity.param.PaymentInitiateParam;
import ai.showlab.bff.entity.param.PaymentStatusQueryParam;
import ai.showlab.bff.entity.vo.v1.PaymentInitiateVo;
import ai.showlab.bff.entity.vo.v1.PaymentStatusVo;
import ai.showlab.bff.service.v1.payment.IPaymentService;
import ai.showlab.common.core.web.domain.RestResult;
import com.ruoyi.common.core.exception.ServiceException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 支付控制器
 * <p>
 * 提供支付发起、回调处理、状态查询等API接口。
 * 支持多种支付网关，与现有的billing系统集成。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/payment")
@Tag(name = "支付管理", description = "支付相关API接口")
public class PaymentController {

    @Autowired
    private IPaymentService paymentService;

    /**
     * 发起支付
     * <p>
     * 基于已创建的订单发起支付，返回支付所需的信息。
     * 支持多种支付方式：重定向、二维码、表单提交、JSAPI等。
     * </p>
     *
     * @param requestParams 支付发起参数
     * @return 支付发起结果
     */
    @PostMapping("/initiate")
    @Operation(summary = "发起支付", description = "基于订单发起支付，返回支付URL、二维码等信息")
    @ApiParamValidate
    public ResponseEntity<RestResult> initiatePayment(RequestParams<PaymentInitiateParam> requestParams) {
        return executeWithTryCatch(() -> {
            PaymentInitiateVo result = paymentService.initiatePayment(requestParams);
            return RestResult.ok("支付发起成功", result);
        }, "");
    }

    /**
     * 查询支付状态
     * <p>
     * 查询订单的支付状态，可选择是否强制从支付网关查询最新状态。
     * 默认优先使用缓存数据，提高查询性能。
     * </p>
     *
     * @param requestParams 支付状态查询参数
     * @return 支付状态信息
     */
    @PostMapping("/status")
    @Operation(summary = "查询支付状态", description = "查询订单的支付状态")
    @ApiAuth
    @ApiParamValidate
    public AjaxResult queryPaymentStatus(@RequestBody RequestParams<PaymentStatusQueryParam> requestParams) {
        return executeWithTryCatch(() -> {
            PaymentStatusVo result = paymentService.queryPaymentStatus(requestParams);
            return AjaxResult.success("查询成功", result);
        });
    }

    /**
     * 取消支付
     * <p>
     * 取消未支付的订单，释放冻结资源。
     * 只能取消状态为"待支付"的订单。
     * </p>
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    @PostMapping("/cancel/{orderNo}")
    @Operation(summary = "取消支付", description = "取消未支付的订单")
    @ApiAuth
    public AjaxResult cancelPayment(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        return executeWithTryCatch(() -> {
            // 这里需要获取当前会员ID，实际实现中应该从认证上下文获取
            Long currentMemberId = getCurrentMemberId();
            boolean result = paymentService.cancelPayment(orderNo, currentMemberId);
            if (result) {
                return AjaxResult.success("支付取消成功");
            } else {
                return AjaxResult.error("支付取消失败");
            }
        });
    }

    /**
     * 支付回调接口（支付宝）
     * <p>
     * 处理支付宝的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果，返回"success"表示处理成功
     */
    @PostMapping("/callback/alipay")
    @Operation(summary = "支付宝回调", description = "处理支付宝异步通知")
    public String alipayCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "alipay");
            
            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);
            
            if (result) {
                log.info("支付宝回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return "success";
            } else {
                log.error("支付宝回调处理失败，订单号: {}", callbackParam.getOrderNo());
                return "fail";
            }
        } catch (Exception e) {
            log.error("支付宝回调处理异常", e);
            return "fail";
        }
    }

    /**
     * 支付回调接口（微信支付）
     * <p>
     * 处理微信支付的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果，返回XML格式的响应
     */
    @PostMapping("/callback/wechat")
    @Operation(summary = "微信支付回调", description = "处理微信支付异步通知")
    public String wechatCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "wechat_pay");
            
            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);
            
            if (result) {
                log.info("微信支付回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
            } else {
                log.error("微信支付回调处理失败，订单号: {}", callbackParam.getOrderNo());
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>";
            }
        } catch (Exception e) {
            log.error("微信支付回调处理异常", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    /**
     * 支付回调接口（PayPal）
     * <p>
     * 处理PayPal的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @PostMapping("/callback/paypal")
    @Operation(summary = "PayPal回调", description = "处理PayPal异步通知")
    public AjaxResult paypalCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "paypal");
            
            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);
            
            if (result) {
                log.info("PayPal回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return AjaxResult.success("处理成功");
            } else {
                log.error("PayPal回调处理失败，订单号: {}", callbackParam.getOrderNo());
                return AjaxResult.error("处理失败");
            }
        } catch (Exception e) {
            log.error("PayPal回调处理异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 执行业务逻辑并处理异常
     *
     * @param supplier 业务逻辑供应商
     * @return 执行结果
     */
    private AjaxResult executeWithTryCatch(java.util.function.Supplier<AjaxResult> supplier) {
        try {
            return supplier.get();
        } catch (ServiceException e) {
            log.error("业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 获取当前会员ID
     *
     * @return 会员ID
     */
    private Long getCurrentMemberId() {
        return BffKit.getCurrentMemberId();
    }

    /**
     * 构建支付回调参数
     *
     * @param request     HTTP请求
     * @param gatewayCode 支付网关编码
     * @return 回调参数
     */
    private PaymentCallbackParam buildCallbackParam(HttpServletRequest request, String gatewayCode) {
        PaymentCallbackParam param = new PaymentCallbackParam();
        
        // TODO: 根据不同支付网关解析回调参数
        // 这里是示例实现，实际需要根据各支付网关的回调格式进行解析
        
        // 获取所有请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        
        // 根据支付网关类型解析参数
        switch (gatewayCode) {
            case "alipay":
                parseAlipayCallback(param, parameterMap);
                break;
            case "wechat_pay":
                parseWechatCallback(param, request);
                break;
            case "paypal":
                parsePaypalCallback(param, parameterMap);
                break;
            default:
                throw new ServiceException("不支持的支付网关: " + gatewayCode);
        }
        
        return param;
    }

    /**
     * 解析支付宝回调参数
     */
    private void parseAlipayCallback(PaymentCallbackParam param, Map<String, String[]> parameterMap) {
        // TODO: 实现支付宝回调参数解析
        // 示例实现
        param.setOrderNo(getParameterValue(parameterMap, "out_trade_no"));
        param.setGatewayTransactionId(getParameterValue(parameterMap, "trade_no"));
        param.setPaymentStatus("success".equals(getParameterValue(parameterMap, "trade_status")) ? "success" : "failed");
    }

    /**
     * 解析微信支付回调参数
     */
    private void parseWechatCallback(PaymentCallbackParam param, HttpServletRequest request) {
        // TODO: 实现微信支付回调参数解析（需要解析XML）
        // 示例实现
        param.setOrderNo("example_order_no");
        param.setGatewayTransactionId("example_transaction_id");
        param.setPaymentStatus("success");
    }

    /**
     * 解析PayPal回调参数
     */
    private void parsePaypalCallback(PaymentCallbackParam param, Map<String, String[]> parameterMap) {
        // TODO: 实现PayPal回调参数解析
        // 示例实现
        param.setOrderNo(getParameterValue(parameterMap, "custom"));
        param.setGatewayTransactionId(getParameterValue(parameterMap, "txn_id"));
        param.setPaymentStatus("Completed".equals(getParameterValue(parameterMap, "payment_status")) ? "success" : "failed");
    }

    /**
     * 获取参数值
     */
    private String getParameterValue(Map<String, String[]> parameterMap, String key) {
        String[] values = parameterMap.get(key);
        return values != null && values.length > 0 ? values[0] : null;
    }
}
